using UnityEngine;
using UnityEditor;

/// <summary>
/// 氮气系统测试工具 - 用于验证氮气消耗计算是否正确
/// </summary>
public class NitroSystemTester : EditorWindow
{
    [MenuItem("Tools/Vehicle/Nitro System Tester")]
    public static void ShowWindow()
    {
        GetWindow<NitroSystemTester>("Nitro System Tester");
    }

    private VehicleData testVehicleData;
    private PartData testNitroPartData;

    private void OnGUI()
    {
        GUILayout.Label("氮气系统测试工具", EditorStyles.boldLabel);
        GUILayout.Space(10);

        // 选择测试车辆
        GUILayout.Label("选择测试车辆数据:", EditorStyles.boldLabel);
        testVehicleData = (VehicleData)EditorGUILayout.ObjectField("Vehicle Data", testVehicleData, typeof(VehicleData), false);

        GUILayout.Space(10);

        // 选择氮气零件
        GUILayout.Label("选择氮气零件 (可选):", EditorStyles.boldLabel);
        testNitroPartData = (PartData)EditorGUILayout.ObjectField("Nitro Part Data", testNitroPartData, typeof(PartData), false);

        GUILayout.Space(20);

        if (GUILayout.Button("计算氮气参数", GUILayout.Height(30)))
        {
            CalculateNitroParameters();
        }

        GUILayout.Space(10);

        if (GUILayout.Button("测试所有氮气零件", GUILayout.Height(30)))
        {
            TestAllNitroParts();
        }
    }

    private void CalculateNitroParameters()
    {
        if (testVehicleData == null)
        {
            EditorUtility.DisplayDialog("错误", "请先选择一个车辆数据！", "确定");
            return;
        }

        // 基础参数
        float baseConsumption = testVehicleData.m_BaseNitroConsumptionRate;
        float baseRegeneration = testVehicleData.m_BaseNitroRegenerationRate;
        float baseCapacity = testVehicleData.m_BaseMaxNitroCapacity;
        float baseForce = testVehicleData.m_BaseNitroForceMagnitude;
        float baseDelay = testVehicleData.m_BaseNitroRegenerationDelay;

        // 最终参数（应用零件加成后）
        float finalConsumption = baseConsumption;
        float finalRegeneration = baseRegeneration;
        float finalCapacity = baseCapacity;
        float finalForce = baseForce;
        float finalDelay = baseDelay;

        string partInfo = "无氮气零件";

        if (testNitroPartData != null && testNitroPartData.PartCategoryProperty == PartCategory.Nitro)
        {
            finalCapacity += testNitroPartData.MaxNitroCapacityBonus;
            finalConsumption *= testNitroPartData.NitroConsumptionEfficiencyMultiplier;
            finalForce += testNitroPartData.NitroForceMagnitudeBonus;
            finalRegeneration += testNitroPartData.NitroRegenerationRateBonus;
            finalDelay -= testNitroPartData.NitroRegenerationDelayReductionBonus;

            // 应用限制
            finalCapacity = Mathf.Max(10, finalCapacity);
            finalConsumption = Mathf.Clamp(finalConsumption, 2f, 50f);
            finalForce = Mathf.Max(0, finalForce);
            finalRegeneration = Mathf.Max(0, finalRegeneration);
            finalDelay = Mathf.Max(0.05f, finalDelay);

            partInfo = $"装备零件: {testNitroPartData.PartName}";
        }

        // 计算使用时间和恢复时间
        float usageTime = finalCapacity / finalConsumption;
        float recoveryTime = finalDelay + (finalCapacity / finalRegeneration);

        string result = $"=== 氮气系统参数计算结果 ===\n\n" +
                       $"车辆: {testVehicleData.m_VehicleName}\n" +
                       $"{partInfo}\n\n" +
                       $"【基础参数】\n" +
                       $"容量: {baseCapacity:F1}\n" +
                       $"消耗速率: {baseConsumption:F1}/秒\n" +
                       $"恢复速率: {baseRegeneration:F1}/秒\n" +
                       $"恢复延迟: {baseDelay:F1}秒\n" +
                       $"推进力: {baseForce:F0}\n\n" +
                       $"【最终参数】\n" +
                       $"容量: {finalCapacity:F1}\n" +
                       $"消耗速率: {finalConsumption:F1}/秒\n" +
                       $"恢复速率: {finalRegeneration:F1}/秒\n" +
                       $"恢复延迟: {finalDelay:F1}秒\n" +
                       $"推进力: {finalForce:F0}\n\n" +
                       $"【性能分析】\n" +
                       $"持续使用时间: {usageTime:F1}秒\n" +
                       $"完全恢复时间: {recoveryTime:F1}秒\n" +
                       $"消耗/恢复比: {finalConsumption/finalRegeneration:F2}";

        Debug.Log(result);
        EditorUtility.DisplayDialog("氮气参数计算结果", result, "确定");
    }

    private void TestAllNitroParts()
    {
        if (testVehicleData == null)
        {
            EditorUtility.DisplayDialog("错误", "请先选择一个车辆数据！", "确定");
            return;
        }

        // 查找所有氮气零件
        string[] partGuids = AssetDatabase.FindAssets("t:PartData");
        string results = $"=== 所有氮气零件测试结果 ===\n车辆: {testVehicleData.m_VehicleName}\n\n";

        // 无零件情况
        float baseConsumption = testVehicleData.m_BaseNitroConsumptionRate;
        float baseRegeneration = testVehicleData.m_BaseNitroRegenerationRate;
        float baseCapacity = testVehicleData.m_BaseMaxNitroCapacity;
        float baseUsageTime = baseCapacity / baseConsumption;
        
        results += $"【无氮气零件】\n";
        results += $"消耗: {baseConsumption:F1}/秒, 恢复: {baseRegeneration:F1}/秒\n";
        results += $"持续时间: {baseUsageTime:F1}秒\n\n";

        foreach (string guid in partGuids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            PartData partData = AssetDatabase.LoadAssetAtPath<PartData>(path);

            if (partData != null && partData.PartCategoryProperty == PartCategory.Nitro)
            {
                float finalConsumption = baseConsumption * partData.NitroConsumptionEfficiencyMultiplier;
                float finalRegeneration = baseRegeneration + partData.NitroRegenerationRateBonus;
                float finalCapacity = baseCapacity + partData.MaxNitroCapacityBonus;
                
                finalConsumption = Mathf.Clamp(finalConsumption, 2f, 50f);
                finalCapacity = Mathf.Max(10, finalCapacity);
                
                float usageTime = finalCapacity / finalConsumption;
                
                results += $"【{partData.PartName}】\n";
                results += $"消耗: {finalConsumption:F1}/秒, 恢复: {finalRegeneration:F1}/秒\n";
                results += $"容量: {finalCapacity:F1}, 持续时间: {usageTime:F1}秒\n";
                results += $"效率系数: {partData.NitroConsumptionEfficiencyMultiplier:F2}\n\n";
            }
        }

        Debug.Log(results);
        EditorUtility.DisplayDialog("所有氮气零件测试结果", results, "确定");
    }
}
